<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <img src="/logo.png" alt="logo" class="logo" />
        <h1 class="title">华宇收资管理系统</h1>
        <p class="subtitle">请登录您的账户</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item prop="city">
          <el-select
            v-model="loginForm.city"
            placeholder="请选择地市"
            size="large"
            style="width: 100%"
            clearable
          >
            <el-option label="VIP用户" value="VIP" />
            <el-option label="驻马店" value="驻马店" />
            <el-option label="确山" value="确山" />
            <el-option label="泌阳" value="泌阳" />
            <el-option label="汝南" value="汝南" />
            <el-option label="平舆" value="平舆" />
            <el-option label="新蔡" value="新蔡" />
            <el-option label="上蔡" value="上蔡" />
            <el-option label="西平" value="西平" />
            <el-option label="遂平" value="遂平" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="authStore.isLoading"
            @click="handleLogin"
          >
            {{ authStore.isLoading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  city: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  city: [
    { required: true, message: '请选择地市', trigger: 'change' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (valid) {
      const success = await authStore.login(loginForm)
      if (success) {
        router.push('/')
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

.login-header {
  text-align: center;
  padding: 40px 30px 30px;
  background: linear-gradient(135deg, #00706b 0%, #4a9b96 100%);
  color: white;
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 1px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.login-form {
  padding: 30px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(0, 112, 107, 0.2);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #00706b 0%, #4a9b96 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #005a56 0%, #3d8a85 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 112, 107, 0.3);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }
  
  .login-card {
    max-width: 100%;
    margin: 0;
  }
  
  .login-header {
    padding: 30px 20px 20px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .logo {
    width: 50px;
    height: 50px;
  }
  
  .login-form {
    padding: 20px;
  }
}
</style>
