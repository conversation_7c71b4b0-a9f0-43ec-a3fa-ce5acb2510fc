// 主样式文件 - 从原有的mycss.css迁移核心样式

// 变量定义
$primary-color: #00706b;
$primary-light: #4a9b96;
$primary-dark: #005a56;
$background-color: #f8f9fa;
$border-color: #e0e0e0;
$text-color: #333;
$text-secondary: #666;
$text-muted: #9ca3af;

// 基础样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Microsoft YaHei", sans-serif;
}

body {
  background-color: $background-color;
  color: $text-color;
  line-height: 1.6;
}

// 布局相关样式
.main-container {
  display: flex;
  height: calc(100vh - 80px); // 减去头部高度
  overflow: hidden;
}

.screen {
  height: 100%;
  overflow-y: auto;
  padding: 15px;
  border-right: 1px solid $border-color;
  background-color: white;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
}

// 标题样式
h2, h3 {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid $border-color;
  color: #2c3e50;
  font-weight: 600;
}

h3 {
  font-size: 17px;
  letter-spacing: 0.5px;
}

// 无选择状态样式
.no-selection {
  color: $text-muted;
  font-style: italic;
  text-align: center;
  margin: 40px 20px;
  padding: 30px;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-radius: 16px;
  border: 2px dashed #d1d5db;
  font-size: 15px;
  line-height: 1.6;
  position: relative;

  &::before {
    content: "📋";
    display: block;
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.6;
  }
}

// 加载状态样式
.loading-container {
  text-align: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid $primary-color;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto 15px;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 18px;
  color: $text-color;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 按钮样式
.btn-primary-custom {
  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 112, 107, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &:hover {
    background: linear-gradient(135deg, $primary-dark 0%, #3d8a85 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 112, 107, 0.35);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 112, 107, 0.3);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    height: auto;
    overflow: visible;
  }

  .screen {
    width: 100% !important;
    height: auto;
    border-right: none;
    border-bottom: none;
    padding: 15px;
    overflow-y: visible;
    display: none;

    &.active {
      display: block;
    }
  }

  h3 {
    font-size: 16px;
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .no-selection {
    font-size: 15px;
    margin-top: 30px;
    padding: 20px;
    line-height: 1.5;
  }
}

// 平板电脑适配 (769px - 1024px)
@media (min-width: 769px) and (max-width: 1024px) {
  .main-container {
    gap: 8px;
  }

  .screen {
    padding: 18px;
  }
}
