<template>
  <div 
    v-if="visible" 
    :class="['global-notification', { 'error': isError, 'success': !isError }]"
    @click="hide"
  >
    <el-icon class="notification-icon">
      <SuccessFilled v-if="!isError" />
      <CircleCloseFilled v-else />
    </el-icon>
    <span class="notification-text">{{ message }}</span>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'

// 响应式数据
const visible = ref(false)
const message = ref('')
const isError = ref(false)
let hideTimer = null

// 显示通知的方法
const show = (msg, error = false) => {
  message.value = msg
  isError.value = error
  visible.value = true
  
  // 清除之前的定时器
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
  
  // 3秒后自动隐藏
  hideTimer = setTimeout(() => {
    hide()
  }, 3000)
}

// 隐藏通知的方法
const hide = () => {
  visible.value = false
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
}

// 全局事件监听
const handleGlobalNotification = (event) => {
  show(event.detail.message, event.detail.isError)
}

onMounted(() => {
  // 监听全局通知事件
  window.addEventListener('global-notification', handleGlobalNotification)
})

onUnmounted(() => {
  window.removeEventListener('global-notification', handleGlobalNotification)
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
})

// 暴露方法给父组件使用
defineExpose({
  show,
  hide
})
</script>

<style lang="scss" scoped>
.global-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  left: 20px;
  max-width: 400px;
  margin: 0 auto;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  
  &.success {
    background-color: rgba(46, 204, 113, 0.95);
    color: white;
    border: 1px solid rgba(46, 204, 113, 0.3);
  }
  
  &.error {
    background-color: rgba(231, 76, 60, 0.95);
    color: white;
    border: 1px solid rgba(231, 76, 60, 0.3);
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}

.notification-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.notification-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

// 移动端适配
@media (max-width: 768px) {
  .global-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    padding: 12px 15px;
    font-size: 14px;
    text-align: center;
    max-width: none;
  }
  
  .notification-text {
    font-size: 14px;
  }
}

// 小屏幕设备适配
@media (max-width: 480px) {
  .global-notification {
    padding: 10px 12px;
  }
  
  .notification-icon {
    font-size: 16px;
  }
  
  .notification-text {
    font-size: 13px;
  }
}
</style>
