<template>
  <div id="app">
    <!-- 全局通知组件 -->
    <GlobalNotification />
    
    <!-- 路由视图 -->
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import GlobalNotification from '@/components/common/GlobalNotification.vue'

const authStore = useAuthStore()

onMounted(() => {
  // 初始化应用时检查用户认证状态
  authStore.checkAuth()
})
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Microsoft YaHei", sans-serif;
}

html, body {
  height: 100%;
  overflow: hidden;
}

#app {
  height: 100vh;
  background-color: #f8f9fa;
}

// Element Plus 主题定制
:root {
  --el-color-primary: #00706b;
  --el-color-primary-light-3: #4a9b96;
  --el-color-primary-light-5: #7fb5b1;
  --el-color-primary-light-7: #b3cfcc;
  --el-color-primary-light-8: #d9e9e8;
  --el-color-primary-light-9: #ecf4f4;
  --el-color-primary-dark-2: #005a56;
}

// 自定义滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 移动端适配
@media (max-width: 768px) {
  body {
    overflow: auto;
    height: auto;
    min-height: 100vh;
  }
  
  #app {
    height: auto;
    min-height: 100vh;
  }
}
</style>
