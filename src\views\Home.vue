<template>
  <div class="home-container">
    <!-- 头部标题栏 -->
    <HeaderTitle />
    
    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 地市导航 -->
      <CityNavigation />
      
      <!-- 项目列表 -->
      <ProjectList />
      
      <!-- 审核状态 -->
      <ReviewStatus />
      
      <!-- 用户详情侧边栏 -->
      <UserInfoSidebar />
    </div>
    
    <!-- 移动端导航栏 -->
    <MobileNavigation />
    
    <!-- 全局加载指示器 -->
    <GlobalLoading v-if="isLoading" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useProjectStore } from '@/stores/project'
import { useUIStore } from '@/stores/ui'

// 组件导入
import HeaderTitle from '@/components/layout/HeaderTitle.vue'
import CityNavigation from '@/components/navigation/CityNavigation.vue'
import ProjectList from '@/components/project/ProjectList.vue'
import ReviewStatus from '@/components/review/ReviewStatus.vue'
import UserInfoSidebar from '@/components/user/UserInfoSidebar.vue'
import MobileNavigation from '@/components/mobile/MobileNavigation.vue'
import GlobalLoading from '@/components/common/GlobalLoading.vue'

// 状态管理
const projectStore = useProjectStore()
const uiStore = useUIStore()

// 响应式数据
const isLoading = ref(true)

// 初始化应用数据
const initializeApp = async () => {
  try {
    isLoading.value = true
    
    // 加载项目数据
    await projectStore.loadProjectsData()
    
    // 恢复保存的导航状态
    projectStore.restoreNavigationState()
    
  } catch (error) {
    console.error('初始化应用失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 处理窗口大小变化
const handleResize = () => {
  uiStore.updateScreenSize()
}

onMounted(async () => {
  // 初始化UI状态
  uiStore.updateScreenSize()
  
  // 初始化应用数据
  await initializeApp()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #f8f9fa;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

// 移动端适配
@media (max-width: 768px) {
  .home-container {
    height: auto;
    min-height: 100vh;
    padding-bottom: 70px; // 为底部导航栏留出空间
    overflow: auto;
  }
  
  .main-container {
    flex-direction: column;
    height: auto;
    overflow: visible;
  }
}
</style>
