import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('access_token') || '')
  const userCity = ref(localStorage.getItem('city') || '')
  const userName = ref(localStorage.getItem('username') || '')
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userInfo = computed(() => ({
    city: userCity.value,
    name: userName.value
  }))

  // 方法
  const login = async (credentials) => {
    isLoading.value = true
    try {
      // 这里应该调用实际的登录API
      // 暂时使用模拟数据
      const response = await mockLogin(credentials)
      
      if (response.success) {
        token.value = response.token
        userCity.value = response.city
        userName.value = response.username
        
        // 保存到localStorage
        localStorage.setItem('access_token', response.token)
        localStorage.setItem('city', response.city)
        localStorage.setItem('username', response.username)
        
        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error('登录失败，请重试')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    token.value = ''
    userCity.value = ''
    userName.value = ''
    
    // 清除localStorage
    localStorage.removeItem('access_token')
    localStorage.removeItem('city')
    localStorage.removeItem('username')
    
    // 清除其他相关的localStorage数据
    localStorage.removeItem('selectedCity')
    localStorage.removeItem('selectedType')
    localStorage.removeItem('expandedCities')
    localStorage.removeItem('selectedProject')
    localStorage.removeItem('selectedFile')
    localStorage.removeItem('userinfoSidebarCollapsed')
    
    ElMessage.success('已退出登录')
  }

  const checkAuth = () => {
    const savedToken = localStorage.getItem('access_token')
    const savedCity = localStorage.getItem('city')
    const savedUsername = localStorage.getItem('username')
    
    if (savedToken && savedCity && savedUsername) {
      token.value = savedToken
      userCity.value = savedCity
      userName.value = savedUsername
    }
  }

  // 模拟登录函数（实际项目中应该替换为真实的API调用）
  const mockLogin = async (credentials) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟登录验证
        if (credentials.username && credentials.password) {
          resolve({
            success: true,
            token: 'mock_token_' + Date.now(),
            city: credentials.city || 'VIP',
            username: credentials.username
          })
        } else {
          resolve({
            success: false,
            message: '用户名或密码不能为空'
          })
        }
      }, 1000)
    })
  }

  return {
    // 状态
    token,
    userCity,
    userName,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userInfo,
    
    // 方法
    login,
    logout,
    checkAuth
  }
})
